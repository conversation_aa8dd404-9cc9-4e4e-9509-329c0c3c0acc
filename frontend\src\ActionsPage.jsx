import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || "http://localhost:8000";

function ActionsPage({ user, setUser }) {
  const navigate = useNavigate();
  const [pendingTickets, setPendingTickets] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPendingTickets();
  }, []);

  const fetchPendingTickets = async () => {
    try {
      const accessToken = localStorage.getItem("access");
      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPendingTickets(data);
      } else {
        console.error("Failed to fetch pending tickets");
      }
    } catch (error) {
      console.error("Error fetching pending tickets:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRaiseNewTicket = () => {
    navigate("/new-ticket");
  };

  const handleUsePendingTicket = () => {
    if (pendingTickets.length === 0) {
      alert("No pending tickets found. Please raise a new ticket.");
      return;
    }

    if (pendingTickets.length === 1) {
      // Directly redirect to chatbot with the single ticket
      navigate(`/chatbot/${pendingTickets[0].ticket_number}`);
    } else {
      // Show selection modal for multiple tickets
      setShowTicketSelection(true);
    }
  };

  const handleGeneralQueries = () => {
    navigate("/chatbot/general");
  };

  const [showTicketSelection, setShowTicketSelection] = useState(false);

  const handleTicketSelect = (ticketNumber) => {
    setShowTicketSelection(false);
    navigate(`/chatbot/${ticketNumber}`);
  };

  if (loading) {
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      textAlign: "center",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ marginBottom: "30px", color: "#333" }}>
        Welcome, {user?.name || "User"}!
      </h1>
      
      <p style={{ marginBottom: "40px", color: "#666", fontSize: "16px" }}>
        What would you like to do today?
      </p>

      <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        {/* Raise New Ticket Button */}
        <button
          onClick={handleRaiseNewTicket}
          style={{
            padding: "20px 30px",
            fontSize: "18px",
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            borderRadius: "8px",
            cursor: "pointer",
            transition: "background-color 0.3s",
          }}
          onMouseOver={(e) => e.target.style.backgroundColor = "#45a049"}
          onMouseOut={(e) => e.target.style.backgroundColor = "#4CAF50"}
        >
          🎫 Raise New Ticket
        </button>

        {/* Use Pending Ticket Button */}
        <button
          onClick={handleUsePendingTicket}
          style={{
            padding: "20px 30px",
            fontSize: "18px",
            backgroundColor: "#2196F3",
            color: "white",
            border: "none",
            borderRadius: "8px",
            cursor: "pointer",
            transition: "background-color 0.3s",
          }}
          onMouseOver={(e) => e.target.style.backgroundColor = "#1976D2"}
          onMouseOut={(e) => e.target.style.backgroundColor = "#2196F3"}
        >
          📋 Use Pending Ticket ({pendingTickets.length})
        </button>

        {/* General Queries Button */}
        <button
          onClick={handleGeneralQueries}
          style={{
            padding: "20px 30px",
            fontSize: "18px",
            backgroundColor: "#FF9800",
            color: "white",
            border: "none",
            borderRadius: "8px",
            cursor: "pointer",
            transition: "background-color 0.3s",
          }}
          onMouseOver={(e) => e.target.style.backgroundColor = "#F57C00"}
          onMouseOut={(e) => e.target.style.backgroundColor = "#FF9800"}
        >
          💬 General Queries
        </button>
      </div>

      {/* Ticket Selection Modal */}
      {showTicketSelection && (
        <div style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0,0,0,0.5)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: "white",
            padding: "30px",
            borderRadius: "10px",
            maxWidth: "500px",
            width: "90%",
            maxHeight: "70vh",
            overflowY: "auto"
          }}>
            <h3 style={{ marginBottom: "20px" }}>Select a Pending Ticket</h3>
            
            <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
              {pendingTickets.map((ticket) => (
                <button
                  key={ticket.ticket_number}
                  onClick={() => handleTicketSelect(ticket.ticket_number)}
                  style={{
                    padding: "15px",
                    border: "1px solid #ddd",
                    borderRadius: "5px",
                    backgroundColor: "#f9f9f9",
                    cursor: "pointer",
                    textAlign: "left",
                    transition: "background-color 0.3s"
                  }}
                  onMouseOver={(e) => e.target.style.backgroundColor = "#e9e9e9"}
                  onMouseOut={(e) => e.target.style.backgroundColor = "#f9f9f9"}
                >
                  <div style={{ fontWeight: "bold" }}>{ticket.ticket_number}</div>
                  <div style={{ fontSize: "14px", color: "#666" }}>
                    {ticket.short_title || `${ticket.product_name} - ${ticket.model}`}
                  </div>
                  <div style={{ fontSize: "12px", color: "#999" }}>
                    Created: {new Date(ticket.created_at).toLocaleDateString()}
                  </div>
                </button>
              ))}
            </div>

            <button
              onClick={() => setShowTicketSelection(false)}
              style={{
                marginTop: "20px",
                padding: "10px 20px",
                backgroundColor: "#f44336",
                color: "white",
                border: "none",
                borderRadius: "5px",
                cursor: "pointer"
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ActionsPage;
