import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || "http://localhost:8000";

function ChatbotPage({ user }) {
  const { ticketId } = useParams();
  const navigate = useNavigate();
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [ticket, setTicket] = useState(null);
  const [stage, setStage] = useState("");
  const [showYesNoButtons, setShowYesNoButtons] = useState(false);
  const [yesNoQuestion, setYesNoQuestion] = useState("");
  const messagesEndRef = useRef(null);

  const isGeneralMode = ticketId === "general" || !ticketId;

  useEffect(() => {
    if (!isGeneralMode && ticketId) {
      fetchTicketDetails();
    } else {
      // For general mode, add welcome message
      setMessages([{
        type: "bot",
        content: "Hello! I'm here to help with general queries about our products and services. How can I assist you today?",
        timestamp: new Date().toISOString()
      }]);
    }
  }, [ticketId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const fetchTicketDetails = async () => {
    try {
      const accessToken = localStorage.getItem("access");
      const response = await fetch(`${BACKEND_URL}/api/ticket/${ticketId}/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (response.ok) {
        const ticketData = await response.json();
        setTicket(ticketData);
        
        // Add welcome message with ticket context
        setMessages([{
          type: "bot",
          content: `Hello! I see you're working on ticket ${ticketData.ticket_number} for your ${ticketData.product_name} - ${ticketData.model}. Please describe the problem you're experiencing, and I'll help you find a solution.`,
          timestamp: new Date().toISOString()
        }]);
      } else {
        console.error("Failed to fetch ticket details");
        navigate("/");
      }
    } catch (error) {
      console.error("Error fetching ticket details:", error);
      navigate("/");
    }
  };

  const sendMessage = async (messageText = inputValue, isYesNoResponse = false) => {
    if (!messageText.trim() && !isYesNoResponse) return;

    const userMessage = {
      type: "user",
      content: messageText,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue("");
    setLoading(true);
    setShowYesNoButtons(false);

    try {
      const accessToken = localStorage.getItem("access");
      const requestBody = {
        query: messageText,
        ticket_mode: !isGeneralMode,
        stage: stage,
      };

      if (!isGeneralMode && ticket) {
        requestBody.ticket_id = ticket.ticket_number;
      }

      const response = await fetch(`${BACKEND_URL}/api/chat/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const data = await response.json();
        
        const botMessage = {
          type: "bot",
          content: data.answer,
          timestamp: new Date().toISOString(),
          files: data.file_suggestions || []
        };

        setMessages(prev => [...prev, botMessage]);

        // Handle different stages and responses
        if (data.stage) {
          setStage(data.stage);

          if (data.stage === "await_close" || data.stage === "unrelated_query" || data.stage === "create_new_ticket" || data.stage === "file_download") {
            setShowYesNoButtons(true);
            setYesNoQuestion(data.answer);
          }
        }

        // Check if the message contains "yes/no" to show buttons
        if (data.answer && data.answer.toLowerCase().includes("(yes/no)")) {
          setShowYesNoButtons(true);
          setYesNoQuestion(data.answer);
        }

        // Handle ticket closure
        if (data.ticket_status === "closed") {
          setTimeout(() => {
            alert("Your ticket has been closed. You will be logged out.");
            handleLogout();
          }, 2000);
        }

      } else {
        const errorData = await response.json();
        console.error("Chat error:", errorData);
        
        const errorMessage = {
          type: "bot",
          content: "Sorry, I encountered an error. Please try again.",
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error("Error sending message:", error);
      
      const errorMessage = {
        type: "bot",
        content: "Sorry, I'm having trouble connecting. Please try again.",
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleYesNoResponse = (response) => {
    sendMessage(response, true);
  };

  const handleLogout = () => {
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
    navigate("/auth");
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleBackToActions = () => {
    navigate("/");
  };

  return (
    <div style={{ 
      display: "flex", 
      flexDirection: "column", 
      height: "100vh",
      fontFamily: "Arial, sans-serif"
    }}>
      {/* Header */}
      <div style={{
        padding: "15px 20px",
        backgroundColor: "#f5f5f5",
        borderBottom: "1px solid #ddd",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <div>
          <h2 style={{ margin: 0, color: "#333" }}>
            {isGeneralMode ? "General Support Chat" : `Ticket: ${ticket?.ticket_number || ticketId}`}
          </h2>
          {!isGeneralMode && ticket && (
            <p style={{ margin: "5px 0 0 0", color: "#666", fontSize: "14px" }}>
              {ticket.product_name} - {ticket.model}
            </p>
          )}
        </div>
        <button
          onClick={handleBackToActions}
          style={{
            padding: "8px 16px",
            backgroundColor: "#2196F3",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer"
          }}
        >
          ← Back to Actions
        </button>
      </div>

      {/* Messages Container */}
      <div style={{
        flex: 1,
        overflowY: "auto",
        padding: "20px",
        backgroundColor: "#fafafa"
      }}>
        {messages.map((message, index) => (
          <div
            key={index}
            style={{
              marginBottom: "15px",
              display: "flex",
              justifyContent: message.type === "user" ? "flex-end" : "flex-start"
            }}
          >
            <div
              style={{
                maxWidth: "70%",
                padding: "12px 16px",
                borderRadius: "18px",
                backgroundColor: message.type === "user" ? "#2196F3" : "#e9e9e9",
                color: message.type === "user" ? "white" : "#333",
                wordWrap: "break-word"
              }}
            >
              <div style={{ whiteSpace: "pre-wrap" }}>{message.content}</div>
              
              {/* File suggestions */}
              {message.files && message.files.length > 0 && (
                <div style={{ marginTop: "10px" }}>
                  <small style={{ color: "#666" }}>Related files:</small>
                  {message.files.map((file, fileIndex) => (
                    <div key={fileIndex} style={{
                      fontSize: "12px",
                      color: "#0066cc",
                      textDecoration: "underline",
                      cursor: "pointer",
                      marginTop: "2px"
                    }}
                    onClick={() => {
                      if (file.url) {
                        window.open(`${BACKEND_URL}${file.url}?token=${localStorage.getItem("access")}`, '_blank');
                      }
                    }}
                    >
                      📄 {file.filename || file}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {loading && (
          <div style={{ display: "flex", justifyContent: "flex-start", marginBottom: "15px" }}>
            <div style={{
              padding: "12px 16px",
              borderRadius: "18px",
              backgroundColor: "#e9e9e9",
              color: "#333"
            }}>
              Typing...
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Yes/No Buttons */}
      {showYesNoButtons && (
        <div style={{
          padding: "15px 20px",
          backgroundColor: "#f0f0f0",
          borderTop: "1px solid #ddd",
          display: "flex",
          justifyContent: "center",
          gap: "15px"
        }}>
          <button
            onClick={() => handleYesNoResponse("Yes")}
            style={{
              padding: "10px 30px",
              backgroundColor: "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
              fontSize: "16px"
            }}
          >
            Yes
          </button>
          <button
            onClick={() => handleYesNoResponse("No")}
            style={{
              padding: "10px 30px",
              backgroundColor: "#f44336",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
              fontSize: "16px"
            }}
          >
            No
          </button>
        </div>
      )}

      {/* Input Area */}
      <div style={{
        padding: "15px 20px",
        backgroundColor: "white",
        borderTop: "1px solid #ddd",
        display: "flex",
        gap: "10px"
      }}>
        <textarea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={isGeneralMode ? "Ask me anything about our products..." : "Describe your problem..."}
          style={{
            flex: 1,
            padding: "12px",
            border: "1px solid #ddd",
            borderRadius: "20px",
            resize: "none",
            minHeight: "20px",
            maxHeight: "100px",
            fontFamily: "Arial, sans-serif",
            fontSize: "14px"
          }}
          rows={1}
        />
        <button
          onClick={() => sendMessage()}
          disabled={loading || !inputValue.trim()}
          style={{
            padding: "12px 20px",
            backgroundColor: loading || !inputValue.trim() ? "#ccc" : "#2196F3",
            color: "white",
            border: "none",
            borderRadius: "20px",
            cursor: loading || !inputValue.trim() ? "not-allowed" : "pointer",
            fontSize: "14px"
          }}
        >
          Send
        </button>
      </div>
    </div>
  );
}

export default ChatbotPage;
