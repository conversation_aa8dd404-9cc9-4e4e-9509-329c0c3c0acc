import os
import fitz  # PyMuPDF
import mysql.connector
from threading import <PERSON>
from datetime import datetime
from fpdf import FPDF
from PIL import Image
from io import BytesIO

# Database config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough'
}

lock = Lock()

def create_image_only_pdf_bytes(pdf_bytes, original_filename):
    """Return bytes of a new PDF with only image-containing pages as full-page images, and also save it to disk."""
    doc = fitz.open(stream=pdf_bytes, filetype="pdf")
    pdf_writer = FPDF()
    image_found = False
    temp_files = []

    for page_num in range(len(doc)):
        page = doc[page_num]
        if page.get_images(full=True):
            image_found = True
            pix = page.get_pixmap(dpi=200)
            img_bytes = pix.tobytes("png")
            img = Image.open(BytesIO(img_bytes))

            temp_path = f"temp_page_{page_num}.png"
            img.save(temp_path)
            temp_files.append(temp_path)

            pdf_writer.add_page()
            pdf_writer.image(temp_path, x=0, y=0, w=210, h=297)

    doc.close()

    if not image_found:
        for temp in temp_files:
            os.remove(temp)
        return None

    # Save to memory
    pdf_data = pdf_writer.output(dest='S').encode('latin1')

    # ✅ Save to disk
    base_name = os.path.splitext(original_filename)[0]
    output_path = os.path.join("generated_image_pdfs", f"{base_name}_images.pdf")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, "wb") as f:
        f.write(pdf_data)
    print(f"🖼️ Saved image-only PDF to: {output_path}")

    # Cleanup
    for temp in temp_files:
        os.remove(temp)

    return pdf_data



def insert_image_pdf_to_db(filename, image_pdf_data, last_modified):
    """Insert image-only PDF into `pdf_image_pages` table."""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO pdf_image_pages (file_name, file_data, last_modified)
            VALUES (%s, %s, %s)
        """, (filename, image_pdf_data, last_modified))

        conn.commit()
        print(f"📥 Inserted image-only PDF: images_ONLY_{filename}")
    except Exception as e:
        print(f"❌ Error inserting image-only PDF '{filename}': {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def upload_pdf_to_db(filename, binary_data, last_modified):
    """Upload original file binary data to MySQL and save image-only version if applicable."""
    with lock:
        try:
            conn = mysql.connector.connect(**db_config)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM pdf_files WHERE file_name = %s", (filename,))
            if cursor.fetchone()[0] > 0:
                print(f"⏩ Skipping '{filename}': already uploaded.")
                return False

            # Insert original PDF
            
            cursor.execute("""
                INSERT INTO pdf_files (file_name, file_data, last_modified)
                VALUES (%s, %s, %s)
            """, (filename, binary_data, last_modified))
            conn.commit()
            print(f"✅ Inserted: {filename}")

            # Extract and save image-only version
            image_pdf_bytes = create_image_only_pdf_bytes(binary_data, filename)
            if image_pdf_bytes:
                image_filename = filename.replace(".pdf", "") + "_images.pdf"
                insert_image_pdf_to_db(image_filename, image_pdf_bytes, last_modified)
            else:
                print(f"⚠️ No image pages to save for {filename}")

            return True
        except Exception as e:
            print(f"❌ Error inserting '{filename}': {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

def bulk_upload_from_folder(folder_path):
    """Uploads all PDFs from a folder."""
    if not os.path.isdir(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return

    files = os.listdir(folder_path)
    for filename in files:
        if not filename.lower().endswith(".pdf"):
            continue
        file_path = os.path.join(folder_path, filename)
        if len(filename) > 255:
            print(f"❌ Skipping '{filename}': filename too long.")
            continue
        try:
            with open(file_path, 'rb') as f:
                binary_data = f.read()
            last_modified = datetime.fromtimestamp(os.path.getmtime(file_path))
            upload_pdf_to_db(filename, binary_data, last_modified)
        except Exception as e:
            print(f"❌ Failed reading '{filename}': {e}")

if __name__ == "__main__":
    folder_path = r"D:\AI-Agent-Chatbot-main\MANUALS"
    bulk_upload_from_folder(folder_path)
    print("🎉 All PDFs processed.")
