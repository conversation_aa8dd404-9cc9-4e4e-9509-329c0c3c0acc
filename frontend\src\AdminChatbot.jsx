import React, { useState, useEffect, useRef } from "react";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function AdminChatbot({ token }) {
  const accessToken = token || localStorage.getItem("access");

  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "bot",
      content: "👋 Welcome, Admin! Ask me anything.",
      timestamp: new Date(),
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [promptTemplate, setPromptTemplate] = useState(null);
  const [pendingFiles, setPendingFiles] = useState(null);
  const [awaitingFileChoice, setAwaitingFileChoice] = useState(false);

  const endRef = useRef(null);
  useEffect(() => endRef.current?.scrollIntoView({ behavior: "smooth" }), [messages]);

  useEffect(() => {
    fetch(`${BACKEND_URL}/api/prompts/?type=chat`)
      .then((r) => r.json())
      .then((d) => setPromptTemplate(d.template))
      .catch(() => {});
  }, []);

  const formatTime = (ts) => ts.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });

  // Render messages with clickable links
  const renderMessages = () =>
    messages.map((m) => (
      <div
        key={m.id}
        style={{ textAlign: m.type === "user" ? "right" : "left", marginBottom: 8 }}
        aria-live="polite"
      >
        <div
          style={{
            display: "inline-block",
            maxWidth: "75%",
            padding: "10px 14px",
            borderRadius: 12,
            backgroundColor: m.type === "user" ? "#dcf8c6" : "#e3e3e3",
            whiteSpace: "pre-wrap",
            color: "#333",
            fontSize: "1rem",
          }}
        >
          {renderWithLinks(m.content)}
          <div style={{ fontSize: "0.7em", color: "#555", marginTop: 6 }}>{formatTime(m.timestamp)}</div>
        </div>
      </div>
    ));

  // Convert markdown-style links [text](url) to anchor tags with token if file links
  const renderWithLinks = (text) => {
    const token = accessToken;
    const lines = text.split("\n");

    return lines.map((line, idx) => {
      const regex = /\[([^\]]+)\]\(([^)]+)\)/g;
      const parts = [];
      let lastIndex = 0;
      let match;
      let keyIndex = 0;

      while ((match = regex.exec(line)) !== null) {
        if (match.index > lastIndex) {
          parts.push(<span key={`${idx}-text-${keyIndex++}`}>{line.substring(lastIndex, match.index)}</span>);
        }

        let href = match[2];

        // Append token if link is to backend files endpoint and token not present
        if (
          (href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith("/api/files/")) &&
          token &&
          !href.includes("token=")
        ) {
          href += href.includes("?") ? `&token=${token}` : `?token=${token}`;
        }

        parts.push(
          <a
            key={`${idx}-link-${keyIndex++}`}
            href={href.startsWith("http") ? href : `${BACKEND_URL}${href}`} // prepend backend if relative URL
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: "#007bff" }}
          >
            {match[1]}
          </a>
        );

        lastIndex = regex.lastIndex;
      }

      if (lastIndex < line.length) {
        parts.push(<span key={`${idx}-text-last`}>{line.substring(lastIndex)}</span>);
      }

      return (
        <div key={idx} style={{ marginBottom: 2 }}>
          {parts.length > 0 ? parts : line}
        </div>
      );
    });
  };

  // Send user input to backend
  const sendToBackend = async (userText) => {
    const historyText = messages
      .filter((m) => m.type === "user" || m.type === "bot")
      .map((m) => `${m.type === "user" ? "User" : "Bot"}: ${m.content}`)
      .join("\n");

    const finalPrompt = promptTemplate
      ? promptTemplate
          .replace("{context_text}", "admin context")
          .replace("{history_text}", historyText)
          .replace("{query}", userText)
      : userText;

    const res = await fetch(`${BACKEND_URL}/api/chat/`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({ query: finalPrompt, ticket_mode: false }),
    });

    const data = await res.json();
    if (!res.ok) throw new Error(data.error || "Server error");

    setMessages((prev) => [
      ...prev,
      { id: Date.now(), type: "bot", content: data.answer || "No answer.", timestamp: new Date() },
    ]);

    // If backend returned related files, prompt user once
    const files = (data.files || []).filter((f) => f.filename);
    if (files.length) {
      setPendingFiles(files);
      setAwaitingFileChoice(true);
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now() + 1,
          type: "bot",
          content: "💡 For full explanation, do you want the related file? (yes/no)",
          timestamp: new Date(),
        },
      ]);
    }
  };

  // Handle user submitting input
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!query.trim() || loading) return;

    const userInput = query.trim();
    setQuery("");
    setMessages((prev) => [
      ...prev,
      { id: Date.now(), type: "user", content: userInput, timestamp: new Date() },
    ]);
    setLoading(true);

    // If waiting for yes/no about related files
    if (awaitingFileChoice) {
      const ans = userInput.toLowerCase();
      if (ans === "yes" && pendingFiles) {
        // Show related files as clickable links with full backend URLs
        const links = pendingFiles
          .map(
            (f, i) =>
              `${i + 1}. [${f.filename}](${f.url || `${BACKEND_URL}/api/files/${encodeURIComponent(f.filename)}`})`
          )
          .join("\n");

        setMessages((prev) => [
          ...prev,
          {
            id: Date.now() + 2,
            type: "bot",
            content: `📎 Related files:\n${links}`,
            timestamp: new Date(),
          },
        ]);
      } else if (ans === "no") {
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now() + 2,
            type: "bot",
            content: "👍 Okay, no files will be sent.",
            timestamp: new Date(),
          },
        ]);
      } else {
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now() + 2,
            type: "bot",
            content: "Please answer 'yes' or 'no'. Do you want the related file?",
            timestamp: new Date(),
          },
        ]);
        setLoading(false);
        return;
      }

      setPendingFiles(null);
      setAwaitingFileChoice(false);
      setLoading(false);
      return;
    }

    // Normal chat flow
    try {
      await sendToBackend(userInput);
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: `❌ ${err.message}`,
          timestamp: new Date(),
        },
      ]);
    }
    setLoading(false);
  };

  return (
    <div className="chat-container">
      <header className="chat-header">
        <h1>ADMIN CHATBOT</h1>
        <p className="subtitle">Technical Support Assistant</p>
      </header>

      <div className="chat-messages" aria-live="polite" aria-relevant="additions">
        {renderMessages()}
        {loading && (
          <div className="typing" aria-label="AI is thinking">
            <span className="dot" style={{ backgroundColor: "#ff3b3f" }}></span>
            <span className="dot" style={{ backgroundColor: "#ff8c00" }}></span>
            <span className="dot" style={{ backgroundColor: "#3bcf4e" }}></span>
            Analyzing…
          </div>
        )}
        <div ref={endRef} />
      </div>

      <form onSubmit={handleSubmit} className="chat-input-form" aria-label="Send message form">
        <textarea
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Type your question here…"
          disabled={loading}
          rows={2}
          aria-label="Chat input"
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              handleSubmit(e);
            }
          }}
        />
        <button type="submit" disabled={loading || !query.trim()} aria-label="Send message">
          {loading ? "…" : "Send"}
        </button>
      </form>
    </div>
  );
}
