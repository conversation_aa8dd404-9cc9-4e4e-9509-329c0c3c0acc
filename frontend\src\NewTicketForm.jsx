import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || "http://localhost:8000";

function NewTicketForm({ user }) {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    product_type: "Camera",
    purchased_from: "",
    year_of_purchase: "",
    product_name: "",
    model: "",
    serial_no: "",
    operating_system: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.purchased_from.trim()) {
      newErrors.purchased_from = "Purchased from is required";
    }
    if (!formData.year_of_purchase.trim()) {
      newErrors.year_of_purchase = "Year of purchase is required";
    }
    if (!formData.product_name.trim()) {
      newErrors.product_name = "Product name is required";
    }
    if (!formData.model.trim()) {
      newErrors.model = "Model is required";
    }
    if (!formData.serial_no.trim()) {
      newErrors.serial_no = "Serial number is required";
    }
    if (!formData.operating_system.trim()) {
      newErrors.operating_system = "Operating system is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const accessToken = localStorage.getItem("access");
      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        // Redirect to chatbot with the new ticket
        navigate(`/chatbot/${data.ticket_number}`);
      } else {
        const errorData = await response.json();
        console.error("Failed to create ticket:", errorData);
        alert("Failed to create ticket. Please try again.");
      }
    } catch (error) {
      console.error("Error creating ticket:", error);
      alert("An error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/");
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ marginBottom: "30px", color: "#333", textAlign: "center" }}>
        Create New Support Ticket
      </h1>
      
      <form onSubmit={handleSubmit} style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        {/* Product Type */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Product Type *
          </label>
          <select
            name="product_type"
            value={formData.product_type}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: "1px solid #ddd",
              borderRadius: "5px",
              fontSize: "16px"
            }}
          >
            <option value="Camera">Camera</option>
            <option value="Frame Grabber">Frame Grabber</option>
            <option value="Accessories">Accessories</option>
            <option value="Software">Software</option>
          </select>
        </div>

        {/* Purchased From */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Purchased From *
          </label>
          <input
            type="text"
            name="purchased_from"
            value={formData.purchased_from}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: `1px solid ${errors.purchased_from ? "#f44336" : "#ddd"}`,
              borderRadius: "5px",
              fontSize: "16px"
            }}
            placeholder="Enter where you purchased the product"
          />
          {errors.purchased_from && (
            <span style={{ color: "#f44336", fontSize: "14px" }}>{errors.purchased_from}</span>
          )}
        </div>

        {/* Year of Purchase */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Year of Purchase *
          </label>
          <input
            type="text"
            name="year_of_purchase"
            value={formData.year_of_purchase}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: `1px solid ${errors.year_of_purchase ? "#f44336" : "#ddd"}`,
              borderRadius: "5px",
              fontSize: "16px"
            }}
            placeholder="Enter year of purchase (e.g., 2023)"
          />
          {errors.year_of_purchase && (
            <span style={{ color: "#f44336", fontSize: "14px" }}>{errors.year_of_purchase}</span>
          )}
        </div>

        {/* Product Name */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Product Name *
          </label>
          <input
            type="text"
            name="product_name"
            value={formData.product_name}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: `1px solid ${errors.product_name ? "#f44336" : "#ddd"}`,
              borderRadius: "5px",
              fontSize: "16px"
            }}
            placeholder="Enter product name"
          />
          {errors.product_name && (
            <span style={{ color: "#f44336", fontSize: "14px" }}>{errors.product_name}</span>
          )}
        </div>

        {/* Model */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Model *
          </label>
          <input
            type="text"
            name="model"
            value={formData.model}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: `1px solid ${errors.model ? "#f44336" : "#ddd"}`,
              borderRadius: "5px",
              fontSize: "16px"
            }}
            placeholder="Enter model number"
          />
          {errors.model && (
            <span style={{ color: "#f44336", fontSize: "14px" }}>{errors.model}</span>
          )}
        </div>

        {/* Serial Number */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Serial Number *
          </label>
          <input
            type="text"
            name="serial_no"
            value={formData.serial_no}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: `1px solid ${errors.serial_no ? "#f44336" : "#ddd"}`,
              borderRadius: "5px",
              fontSize: "16px"
            }}
            placeholder="Enter serial number"
          />
          {errors.serial_no && (
            <span style={{ color: "#f44336", fontSize: "14px" }}>{errors.serial_no}</span>
          )}
        </div>

        {/* Operating System */}
        <div>
          <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
            Operating System *
          </label>
          <input
            type="text"
            name="operating_system"
            value={formData.operating_system}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "10px",
              border: `1px solid ${errors.operating_system ? "#f44336" : "#ddd"}`,
              borderRadius: "5px",
              fontSize: "16px"
            }}
            placeholder="Enter operating system (e.g., Windows 10, Ubuntu 20.04)"
          />
          {errors.operating_system && (
            <span style={{ color: "#f44336", fontSize: "14px" }}>{errors.operating_system}</span>
          )}
        </div>

        {/* Buttons */}
        <div style={{ display: "flex", gap: "15px", marginTop: "20px" }}>
          <button
            type="submit"
            disabled={loading}
            style={{
              flex: 1,
              padding: "15px",
              backgroundColor: loading ? "#ccc" : "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "5px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer",
              transition: "background-color 0.3s"
            }}
          >
            {loading ? "Creating..." : "Create Ticket & Start Chat"}
          </button>
          
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            style={{
              flex: 1,
              padding: "15px",
              backgroundColor: "#f44336",
              color: "white",
              border: "none",
              borderRadius: "5px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer",
              transition: "background-color 0.3s"
            }}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

export default NewTicketForm;
